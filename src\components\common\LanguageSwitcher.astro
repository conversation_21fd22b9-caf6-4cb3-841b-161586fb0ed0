---
export interface Props {
  i18n: any;
  lang: "fr" | "en";
}

const { lang } = Astro.props;
---

<script is:inline define:vars={{ currentLang: lang }}>
  document.addEventListener("DOMContentLoaded", () => {
    const languageSwitcher = document.getElementById("language-switcher");
    if (languageSwitcher) {
      languageSwitcher.addEventListener("click", () => {
        const newLang = currentLang === "fr" ? "en" : "fr";
        const currentUrl = window.location.href;

        if (currentUrl.includes(`/${currentLang}/`)) {
          window.location.href = currentUrl.replace(
            `/${currentLang}/`,
            `/${newLang}/`
          );
        } else if (currentUrl.endsWith(`/${currentLang}`)) {
          window.location.href = currentUrl.replace(
            `/${currentLang}`,
            `/${newLang}`
          );
        } else {
          window.location.href = currentUrl.replace(/\/$/, "") + `/${newLang}`;
        }
      });
    }
  });
</script>

<div class="language-switcher">
  <button
    type="button"
    id="language-switcher"
    aria-label={`Changer vers ${lang === "fr" ? "English" : "Français"}`}
  >
    {lang === "fr" ? "EN" : "FR"}
  </button>
</div>

<style>
  .language-switcher {
    display: inline-flex;
  }

  button {
    padding: 0.9rem 1.1rem;
    min-width: 60px;
    background: color-mix(in srgb, var(--color-surface) 96%, transparent);
    border: 1px solid var(--color-border);
    border-radius: calc(var(--border-radius) + 2px);
    color: var(--color-text-primary);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition:
      background-color 0.2s ease,
      transform 0.1s ease;
  }

  button:hover {
    background: color-mix(in srgb, var(--color-surface) 98%, transparent);
    transform: translateY(-1px);
  }

  button:active {
    background: color-mix(in srgb, var(--color-surface) 98%, transparent);
    transform: translateY(0);
  }

  button:focus-visible {
    outline: 2px solid var(--accent-regular);
    outline-offset: 3px;
  }
</style>
