---
export interface Props {
  i18n: any;
  lang: "fr" | "en";
}

const { i18n, lang } = Astro.props;
---

<div class="language-switcher">
  <select id="language-switcher" name="language" aria-label="Changer la langue">
    {
      ["fr", "en"].map((code) => (
        <option value={code} selected={code === lang}>
          {i18n.common.languages[code]}
        </option>
      ))
    }
  </select>
</div>

<script>
  function handleLanguageChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const newLang = target.value;
    window.location.href = window.location.href.replace(
      /\/[a-z]{2}\//,
      `/${newLang}/`
    );
  }

  // Attach the event listener properly
  document.addEventListener("DOMContentLoaded", () => {
    const languageSwitcher = document.getElementById(
      "language-switcher"
    ) as HTMLSelectElement;
    if (languageSwitcher) {
      languageSwitcher.addEventListener("change", handleLanguageChange);
    }
  });
</script>
