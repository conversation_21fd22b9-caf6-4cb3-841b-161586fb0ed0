---
export interface Props {
  lang: "fr" | "en";
}

const { lang } = Astro.props;
---

<script is:inline define:vars={{ currentLang: lang }}>
  document.addEventListener("DOMContentLoaded", () => {
    const languageSwitcher = document.getElementById("language-switcher");
    if (languageSwitcher) {
      languageSwitcher.addEventListener("click", () => {
        const newLang = currentLang === "fr" ? "en" : "fr";
        window.location.href = window.location.href.replace(
          /\/[a-z]{2}\//,
          `/${newLang}/`
        );
      });
    }
  });
</script>

<div class="language-switcher">
  <button
    type="button"
    id="language-switcher"
    aria-label={`Changer vers ${lang === "fr" ? "English" : "Français"}`}
  >
    {lang === "fr" ? "EN" : "FR"}
  </button>
</div>

<style>
  .language-switcher {
    display: inline-flex;
  }

  select {
    padding: 0.9rem 1.1rem;
    min-width: 180px;
    background: color-mix(in srgb, var(--color-surface) 96%, transparent);
    border: 1px solid var(--color-border);
    border-radius: calc(var(--border-radius) + 2px);
    color: var(--color-text-primary);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
  }

  select::-ms-expand {
    display: none;
  }

  select:focus {
    outline: none;
  }

  select:hover {
    background: color-mix(in srgb, var(--color-surface) 98%, transparent);
  }

  select:active {
    background: color-mix(in srgb, var(--color-surface) 98%, transparent);
  }

  select:focus-visible {
    outline: 2px solid var(--accent-regular);
    outline-offset: 3px;
  }
</style>
