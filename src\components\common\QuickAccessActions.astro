---
import LanguageSwitcher from "./LanguageSwitcher.astro";
import ThemeModeCycle from "./ThemeModeCycle.astro";

export interface Props {
  i18n: any;
  lang: "fr" | "en";
}

const { i18n, lang } = Astro.props as Props;
---

<section
  class={`qa-actions-container`}
  role="group"
  aria-label="Centre d'actions"
>
  <h4 class="no-select">{i18n.quickAccess.actions}</h4>
  <div class="qa-actions-row">
    <div class="qa-actions-row">
      <ThemeModeCycle i18n={i18n} lang={lang} />
    </div>
    <div class="qa-actions-row">
      <LanguageSwitcher i18n={i18n} lang={lang} />
    </div>

    <!-- Slot si un jour tu veux ajouter d’autres actions -->
    <slot />
  </div>
</section>

<style>
  .qa-actions-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-8);
  }

  .qa-actions-container.row {
    flex-direction: row;
    align-items: center;
  }

  .qa-actions-container.column {
    flex-direction: column;
  }

  .qa-actions-container.dense {
    gap: var(--spacing-4);
  }

  .qa-actions-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
  }

  @media (max-width: 720px) {
    .qa-actions-container.row {
      flex-direction: column;
      align-items: flex-start;
    }
  }
</style>
