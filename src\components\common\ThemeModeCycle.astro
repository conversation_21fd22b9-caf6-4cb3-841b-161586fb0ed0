---
import Icon from "./Icon.astro";

export interface Props {
  i18n: any;
  lang: "fr" | "en";
}
const { i18n, lang } = Astro.props;
---

<script
  is:inline
  define:vars={{
    darkText: i18n.quickAccess.theme.dark,
    lightText: i18n.quickAccess.theme.light,
  }}
>
  document.addEventListener("DOMContentLoaded", () => {
    const toggleBtn = document.getElementById("theme-cycle-btn");
    const modeText = document.querySelector("[data-mode-text]");
    const theme = localStorage.getItem("theme");
    modeText.textContent = theme === "dark" ? darkText : lightText;

    if (toggleBtn) {
      toggleBtn.addEventListener("click", () => {
        toggleTheme();
      });
    }

    function toggleTheme() {
      document.documentElement.classList.toggle("theme-dark");
      if (modeText) {
        modeText.textContent =
          modeText.textContent === lightText ? darkText : lightText;
      }

      const isDark = document.documentElement.classList.contains("theme-dark");
      localStorage.setItem("theme", isDark ? "dark" : "light");
    }
  });
</script>

<div class={`theme-mode-cycle`}>
  <button
    type="button"
    class="cycle-btn no-select action-btn"
    id="theme-cycle-btn"
  >
    <span class="icon light"><Icon icon="sun" /></span>
    <span class="icon dark"><Icon icon="moon" /></span>
    <span class="action-label">{i18n.quickAccess.theme.label}</span>
    <span class="action-mode" data-mode-text></span>
  </button>
</div>

<style>
  .theme-mode-cycle {
    display: inline-flex;
  }

  .action-btn {
    position: relative;
    display: grid;
    grid-template-columns: auto 1fr;
    grid-template-rows: auto auto;
    grid-template-areas:
      "icon title"
      "icon state";
    align-items: center;
    column-gap: var(--spacing-4);
    row-gap: var(--spacing-2);

    padding: 0.9rem 1.1rem;
    min-width: 180px;

    background: color-mix(in srgb, var(--color-surface) 96%, transparent);
    border: 1px solid var(--color-border);
    border-radius: calc(var(--border-radius) + 2px);
    color: var(--color-text-primary);
    cursor: pointer;

    transition:
      background-color 0.25s ease,
      border-color 0.25s ease,
      box-shadow 0.25s ease,
      transform 0.12s ease;
  }

  /* halo subtil au hover */
  .action-btn::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: radial-gradient(
        120% 90% at 0% 0%,
        color-mix(in srgb, var(--accent-regular) 12%, transparent) 0%,
        transparent 60%
      )
      no-repeat;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.25s ease;
  }

  .action-btn:hover {
    transform: translateY(-1px);
    background: color-mix(in srgb, var(--color-surface) 98%, transparent);
  }
  .action-btn:hover::before {
    opacity: 0.75;
  }
  .action-btn:active {
    transform: translateY(0);
    box-shadow: 0 6px 18px rgb(0 0 0 / 0.12);
  }
  .action-btn:focus-visible {
    outline: 2px solid var(--accent-regular);
    outline-offset: 3px;
  }

  .action-label {
    grid-area: title;
    font-weight: 700;
    font-size: 0.95rem;
  }

  .action-mode {
    grid-area: state;
    font-size: 0.9rem;
    color: var(--color-text-secondary);
  }

  .cycle-dot {
    inline-size: 0.4rem;
    block-size: 0.4rem;
    border-radius: 999px;
    background: var(--accent-regular);
    opacity: 0.6;
    margin-inline: 0.25rem;
  }

  .icon {
    grid-area: icon;
    inline-size: 36px;
    block-size: 36px;
    display: grid;
    place-items: center;
    border-radius: 10px;
    position: relative;
    background: color-mix(in srgb, var(--color-border) 24%, transparent);

    opacity: 0;
    transform: scale(0.85) rotate(-8deg);
    transition:
      opacity 0.22s ease,
      transform 0.28s cubic-bezier(0.2, 0.8, 0.2, 1),
      filter 0.22s ease;
  }
  .icon svg {
    pointer-events: none;
  }

  :global(html:not(.theme-dark)) .action-btn .icon.light {
    opacity: 1;
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 1px 6px rgb(0 0 0 / 0.12));
  }
  :global(.theme-dark) .action-btn .icon.dark {
    opacity: 1;
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 1px 6px rgb(0 0 0 / 0.18));
  }

  @keyframes popIn {
    from {
      opacity: 0;
      transform: translateY(2px);
      filter: blur(0.4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
      filter: blur(0);
    }
  }
  :global(html:not(.theme-dark)) .action-btn .cycle-mode {
    animation: popIn 0.16s ease both;
  }
  :global(.theme-dark) .action-btn .cycle-mode {
    animation: popIn 0.16s ease both;
  }

  @media (prefers-reduced-motion: reduce) {
    .action-btn {
      transition: none;
    }
    .icon {
      transition: none;
    }
    .action-btn::before {
      display: none;
    }
  }
</style>
