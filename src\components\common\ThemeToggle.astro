---
import Icon from "./Icon.astro";
---

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const toggleBtn = document.getElementById("theme-toggle-btn");

    if (toggleBtn) {
      toggleBtn.addEventListener("click", () => {
        toggleTheme();
      });
    }

    function toggleTheme() {
      document.documentElement.classList.toggle("theme-dark");

      const isDark = document.documentElement.classList.contains("theme-dark");
      localStorage.setItem("theme", isDark ? "dark" : "light");
    }

    // Listen for keydown events on the document
    document.addEventListener("keydown", (e) => {
      if (e.key === "d") {
        toggleTheme();
      }
    });
  });
</script>

<div class="theme-toggle">
  <button id="theme-toggle-btn">
    <span class="icon light"><Icon icon="sun" /></span>
    <span class="icon dark"><Icon icon="moon" /></span>
  </button>
</div>

<style>
  .theme-toggle {
    color: var(--gray-100);
  }

  #theme-toggle-btn {
    background-color: var(--color-solid);
  }

  button {
    display: flex;
    border: 0;
    border-radius: 999rem;
    padding: 0;
    background-color: var(--gray-999);
    box-shadow: inset 0 0 0 1px var(--accent-overlay);
    cursor: pointer;
  }

  .icon {
    z-index: 1;
    position: relative;
    display: flex;
    padding: 0.5rem;
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
    color: var(--accent-overlay);
  }

  .icon.light::before {
    content: "";
    z-index: -1;
    position: absolute;
    inset: 0;
    background-color: var(--accent-regular);
    border-radius: 999rem;
  }

  :global(.theme-dark) .icon.light::before {
    transform: translateX(100%);
  }

  :global(.theme-dark) .icon.dark,
  :global(html:not(.theme-dark)) .icon.light {
    color: var(--accent-text-over);
  }

  :global(.theme-dark) .icon.light,
  :global(html:not(.theme-dark)) .icon.dark {
    color: var(--accent-text-over);
  }

  @media (prefers-reduced-motion: no-preference) {
    .icon,
    .icon.light::before {
      transition:
        transform 0.3s ease,
        color 0.3s ease;
    }
  }
</style>
