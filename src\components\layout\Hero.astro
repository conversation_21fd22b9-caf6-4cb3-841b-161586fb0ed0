---
import { Image } from "astro:assets";
import avatar from "../../assets/images/hero-image.webp";
import Icon from "../common/Icon.astro";
export interface Props {
  i18n: any;
  lang: "fr" | "en";
}
const { i18n } = Astro.props;
---

<section class="hero">
  <div class="container">
    <h3 class="greeting no-select">{i18n.hero.greeting}</h3>
    <h1 class="title">
      <span class="light">{"im" in i18n.hero ? i18n.hero.im : ""}</span>
      <span class="first-name">Jimmy</span>
      <span class="name"><PERSON><PERSON>cher</span>
    </h1>
    <h4 class="subtitle">{i18n.hero.role_description}</h4>

    <a
      class="btn btn--primary"
      href={i18n.hero.cta_contact_href}
      rel="noopener"
    >
      {i18n.hero.cta_contact}
    </a>
    <div class="hero-avatar">
      <div class="gradient-bg"></div>
      <Image
        src={avatar}
        alt="Portrait de Jimmy Gaucher"
        widths={[240, 320, 400]}
        sizes="(max-width: 900px) 70vw, 380px"
        loading="eager"
        decoding="async"
      />
    </div>
  </div>
</section>

<style>
  .hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: clamp(3rem, 6vw, 6rem) var(--spacing-4);
    background: var(--color-background);
    text-align: left;
  }

  .container {
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-width: var(--container-max-width, 1100px);
    padding: 0 var(--spacing-4);
    position: relative;
    z-index: 2;
  }

  .greeting {
    color: var(--color-text-secondary);
  }

  .first-name {
    color: var(--accent-regular);
  }

  .subtitle {
    margin: 0 0 1.25rem;
    color: var(--color-text-secondary);
    white-space: pre-line;
  }
  .hero-avatar {
    position: absolute;
    right: 10%;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    width: 420px;
    aspect-ratio: 3 / 4;
    border-radius: 9999px;
    overflow: hidden;
    z-index: 1;
  }

  .hero-avatar .gradient-bg {
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: linear-gradient(
      to top,
      color-mix(in srgb, var(--accent-regular) 50%, transparent) 0%,
      transparent 100%
    );
  }

  :global(.theme-dark) .hero-avatar .gradient-bg {
    background: linear-gradient(
      to top,
      color-mix(in srgb, var(--accent-regular) 40%, transparent) 0%,
      transparent 100%
    );
  }

  .hero-avatar img {
    position: relative;
    z-index: 1;
    width: 100%;
    height: 100%;
    object-fit: scale-down;
  }

  @media (max-width: 1024px) {
    .hero {
      flex-direction: column;
      justify-content: center;
    }

    .hero-avatar {
      display: none;
    }

    .container {
      text-align: left;
      z-index: 3;
    }

    .actions {
      justify-content: center;
    }
  }
</style>
