---
import QuickAccessTips from "../common/QuickAccessTips.astro";
import CloseButton from "../common/CloseButton.astro";
import QuickAccessActions from "../common/QuickAccessActions.astro";

interface Props {
  githubUsername?: string;
  i18n: any;
  lang: "fr" | "en";
  initialOpen?: boolean;
  keycapLabel: string;
}

const {
  githubUsername = "Jamedie",
  lang,
  initialOpen = false,
} = Astro.props as Props;

// Define the available languages
const languages = [
  { code: "fr", label: "Français" },
  { code: "en", label: "English" },
];

const { i18n, keycapLabel } = Astro.props;
---

<script is:inline>
  document.addEventListener("DOMContentLoaded", () => {
    const root = document.getElementById("quick-access-menu");

    const html = document.documentElement;
    const closeBtn = root?.querySelector(".close-btn");

    // Scroll lock
    const getScrollbarWidth = () =>
      Math.max(0, window.innerWidth - document.documentElement.clientWidth);

    function applyScrollLock() {
      if (!root) return;
      const isOpen = root.getAttribute("data-open") === "1";

      if (isOpen) {
        const sbw = getScrollbarWidth();
        html.style.setProperty("--sbw", sbw + "px");
        html.classList.add("qa-lock-scroll");
      } else {
        html.classList.remove("qa-lock-scroll");
        html.style.removeProperty("--sbw");
      }
    }

    window.addEventListener("resize", () => {
      if (root?.getAttribute("data-open") === "1") {
        html.style.setProperty("--sbw", getScrollbarWidth() + "px");
      }
    });

    applyScrollLock();
    new MutationObserver(applyScrollLock).observe(root, {
      attributes: true,
      attributeFilter: ["data-open"],
    });

    // Fermetures
    closeBtn?.addEventListener("click", () =>
      root.setAttribute("data-open", "0")
    );
    window.addEventListener("keydown", (e) => {
      if (e.key === "Escape" && root.getAttribute("data-open") === "1") {
        e.preventDefault();
        root.setAttribute("data-open", "0");
      }
    });

    //Changement de langue
    const toggleLanguage = () => {
      const currentLang = root.getAttribute("data-lang");
      const newLang = currentLang === "fr" ? "en" : "fr";

      // Changer l'URL pour la nouvelle langue
      const currentPath = window.location.pathname;
      const newPath = currentPath.replace(/^\/[a-z]{2}/, `/${newLang}`);
      window.location.href = newPath;
    };

    // Ecoute des raccourcis clavier
    document.addEventListener("keydown", (e) => {
      if (e.key.toLowerCase() === "l") {
        e.preventDefault();
        toggleLanguage();
      }
    });
  });
</script>

<section
  id="quick-access-menu"
  class="qa-root"
  data-open={initialOpen ? "1" : "0"}
  data-lang={lang || "fr"}
>
  <div class="qa-overlay"></div>

  <div class="qa-panel">
    <CloseButton />

    <div class="right-layout">
      <!-- Actions -->
      <QuickAccessActions i18n={i18n} lang={lang} />
    </div>

    <!-- Raccourcis clavier -->
    <QuickAccessTips
      position="bl"
      offset="var(--spacing-15)"
      i18n={i18n}
      keycapLabel={keycapLabel}
    />
  </div>
</section>

<style>
  #quick-access-menu {
    position: fixed;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;

    width: 100%;
    height: 100vh;

    pointer-events: none;
    transition: opacity 0.3s ease;
  }

  #quick-access-menu[data-open="1"] {
    opacity: 1;
    pointer-events: auto;
  }

  #quick-access-menu[data-open="0"] {
    opacity: 0;
  }

  .qa-overlay {
    position: fixed;
    inset: 0;
    background: var(--color-background-85a);
    backdrop-filter: blur(2px);
    z-index: 0;

    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
  }

  #quick-access-menu[data-open="1"] .qa-overlay {
    opacity: 1;
    pointer-events: auto;
  }

  .qa-panel {
    position: fixed;
    width: 100%;
    height: 100vh;
    z-index: 1;
    pointer-events: none;
  }

  #quick-access-menu[data-open="1"] .qa-panel {
    pointer-events: auto;
  }

  .right-layout {
    position: absolute;
    top: 6rem;
    right: var(--spacing-15);
    bottom: var(--spacing-15);
    width: min(400px, 100%);
    overflow: auto;
    gap: var(--spacing-5);
    display: flex;
    flex-direction: column;
  }
</style>
